import { memo } from "react";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { ThemeToggle } from "@/components/ThemeToggle";
import { BackGround } from "@/components/BackGround";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";
import { AppRouter } from "@/router/AppRouter";

const App = memo(function App() {
  return (
    <ThemeProvider>
      <div className="relative min-h-screen overflow-x-hidden bg-white dark:bg-dark theme-transition">
        <div className="absolute inset-0 z-0">
          <BackGround />
        </div>
        <PerformanceMonitor enabled={false} />
        <div className="relative z-20">
          <ThemeToggle />
        </div>
        <div className="relative z-10">
          <AppRouter />
        </div>
      </div>
    </ThemeProvider>
  );
});

export default App;
