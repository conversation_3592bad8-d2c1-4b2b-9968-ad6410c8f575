import { useEffect, useRef, memo } from "react";

interface PerformanceMonitorProps {
  /** Whether to enable performance monitoring (default: false in production) */
  enabled?: boolean;
  /** Callback for performance metrics */
  onMetrics?: (metrics: PerformanceMetrics) => void;
}

interface PerformanceMetrics {
  fps: number;
  memoryUsage?: number;
  renderTime: number;
  timestamp: number;
}

/**
 * PerformanceMonitor - A lightweight component for monitoring React app performance
 *
 * Features:
 * - FPS monitoring using requestAnimationFrame
 * - Memory usage tracking (when available)
 * - Render time measurement
 * - Minimal performance overhead
 * - Only active in development or when explicitly enabled
 */
export const PerformanceMonitor = memo(function PerformanceMonitor({
  enabled = false,
  onMetrics,
}: PerformanceMonitorProps) {
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const animationFrameRef = useRef<number | null>(null);
  const renderStartRef = useRef(performance.now());

  useEffect(() => {
    if (!enabled) return;

    renderStartRef.current = performance.now();

    const measureFPS = () => {
      const now = performance.now();
      frameCountRef.current++;

      // Calculate FPS every second
      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round(
          (frameCountRef.current * 1000) / (now - lastTimeRef.current)
        );
        const renderTime = now - renderStartRef.current;

        // Get memory usage if available
        const memoryUsage = (performance as any).memory?.usedJSHeapSize;

        const metrics: PerformanceMetrics = {
          fps,
          memoryUsage,
          renderTime,
          timestamp: now,
        };

        onMetrics?.(metrics);

        // Log to console in development
        if (true) {
          console.log(`🚀 Performance Metrics:`, {
            FPS: fps,
            "Render Time": `${renderTime.toFixed(2)}ms`,
            Memory: memoryUsage
              ? `${(memoryUsage / 1024 / 1024).toFixed(2)}MB`
              : "N/A",
          });
        }

        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }

      animationFrameRef.current = requestAnimationFrame(measureFPS);
    };

    animationFrameRef.current = requestAnimationFrame(measureFPS);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled, onMetrics]);

  // This component doesn't render anything
  return null;
});

/**
 * Hook for performance monitoring without rendering a component
 */
export function usePerformanceMonitor(
  enabled: boolean = process.env.NODE_ENV === "development",
  onMetrics?: (metrics: PerformanceMetrics) => void
) {
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    if (!enabled) return;

    const measureFPS = () => {
      const now = performance.now();
      frameCountRef.current++;

      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round(
          (frameCountRef.current * 1000) / (now - lastTimeRef.current)
        );
        const memoryUsage = (performance as any).memory?.usedJSHeapSize;

        const metrics: PerformanceMetrics = {
          fps,
          memoryUsage,
          renderTime: 0, // Not applicable for hook usage
          timestamp: now,
        };

        onMetrics?.(metrics);

        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }

      animationFrameRef.current = requestAnimationFrame(measureFPS);
    };

    animationFrameRef.current = requestAnimationFrame(measureFPS);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled, onMetrics]);
}
