import { useEffect, useState, useRef } from "react";
import { AutoScrollCarousel } from "@/components/shared/AutoScrollCarousel";
import { FreeAuditButton } from "@/components/shared/FreeAuditButton";
// import { AnimatedSectionTitle } from "@/components/AnimatedSectionTitle";

export function Implementation() {
  const [isVisible, setIsVisible] = useState(false);
  const [showTitle, setShowTitle] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const timeoutRefs = useRef<NodeJS.Timeout[]>([]);

  const aiAgentCategories = [
    {
      title: "Sales AI Agent",
      description:
        "Automate lead management, booking appointments, conversions and hand off with human SDRs.",
      features: [
        "Integrated with CRM and Calendar Systems",
        "Automated Nurture Campaigns to book leads",
        "Voice Call, Email and SMS capabilities",
      ],
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" />
        </svg>
      ),
    },
    {
      title: "Marketing AI Agent",
      description:
        "Lead generation, Content creation and intelligent funnels with AI.",
      features: [
        "Re-activate existing lead lists",
        "Create Content on Autopilot",
        "Create customer loyalty through remarketing automation",
      ],
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2ZM4 14L5 17H7L6 14H4ZM9 14L10 17H12L11 14H9ZM14 14L15 17H17L16 14H14ZM19 14L20 17H22L21 14H19Z" />
        </svg>
      ),
    },
    {
      title: "Administrative AI Agent",
      description: "Custom built to help you manage your business.",
      features: [
        "Eliminate the busy work in everyday repetitive tasks",
        "Automate processes within your business so humans can focus on more high leverage work",
        "Everything from Legal to Accounting, to inventory management and more",
      ],
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 15.5C10.07 15.5 8.5 13.93 8.5 12C8.5 10.07 10.07 8.5 12 8.5C13.93 8.5 15.5 10.07 15.5 12C15.5 13.93 13.93 15.5 12 15.5ZM19.43 12.97C19.47 12.65 19.5 12.33 19.5 12C19.5 11.67 19.47 11.34 19.43 11L21.54 9.37C21.73 9.22 21.78 8.95 21.66 8.73L19.66 5.27C19.54 5.05 19.27 4.96 19.05 5.05L16.56 6.05C16.04 5.65 15.48 5.32 14.87 5.07L14.49 2.42C14.46 2.18 14.25 2 14 2H10C9.75 2 9.54 2.18 9.51 2.42L9.13 5.07C8.52 5.32 7.96 5.66 7.44 6.05L4.95 5.05C4.72 4.96 4.46 5.05 4.34 5.27L2.34 8.73C2.21 8.95 2.27 9.22 2.46 9.37L4.57 11C4.53 11.34 4.5 11.67 4.5 12C4.5 12.33 4.53 12.65 4.57 12.97L2.46 14.63C2.27 14.78 2.21 15.05 2.34 15.27L4.34 18.73C4.46 18.95 4.72 19.03 4.95 18.95L7.44 17.94C7.96 18.34 8.52 18.68 9.13 18.93L9.51 21.58C9.54 21.82 9.75 22 10 22H14C14.25 22 14.46 21.82 14.49 21.58L14.87 18.93C15.48 18.68 16.04 18.34 16.56 17.94L19.05 18.95C19.27 19.03 19.54 18.95 19.66 18.73L21.66 15.27C21.78 15.05 21.73 14.78 21.54 14.63L19.43 12.97Z" />
        </svg>
      ),
    },
    {
      title: "Customer Service AI Agent",
      description: "World class customer service, 24/7.",
      features: [
        "Automate FAQ and low priority problem resolutions",
        "Integrate with existing infrastructure to hand off to human CSAs",
        "Intelligent workflows to notify company leadership when necessary",
      ],
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.55 7.78L7.1 9.33C8.8 8.14 10.85 7.5 13 7.5C15.15 7.5 17.2 8.14 18.9 9.33L21 9ZM1 18V20H23V18H1Z" />
        </svg>
      ),
    },
  ];

  // Clear all timeouts
  const clearTimeouts = () => {
    timeoutRefs.current.forEach((timeout) => clearTimeout(timeout));
    timeoutRefs.current = [];
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Clear any existing timeouts
            clearTimeouts();

            // Reset states and start sequence
            setIsVisible(true);
            setShowTitle(true);
            setShowContent(false);

            // After 2 seconds, fade out title
            const titleTimeout = setTimeout(() => {
              setShowTitle(false);
            }, 2000);

            // After 2.5 seconds total, fade in content
            const contentTimeout = setTimeout(() => {
              setShowContent(true);
            }, 2500);

            // Store timeouts for cleanup
            timeoutRefs.current = [titleTimeout, contentTimeout];
          } else {
            // Clear timeouts and reset states when leaving section
            clearTimeouts();
            setIsVisible(false);
            setShowTitle(false);
            setShowContent(false);
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: "-10% 0px -10% 0px",
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    const currentRef = sectionRef.current;
    return () => {
      clearTimeouts();
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <div
      ref={sectionRef}
      className="h-screen w-full overflow-hidden relative theme-transition"
    >
      {/* Animated Section Title */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-1000 ${showTitle && isVisible ? "opacity-100" : "opacity-0"
          }`}
      >
        <div className="text-center">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-brand font-bold text-dark dark:text-slate-100 mb-4 theme-transition px-4 text-center">
            Implementation
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl text-dark dark:text-light font-body theme-transition px-4 text-center">
            AI Agents for Every Business Need
          </p>
        </div>
      </div>

      {/* Technology Stack Content */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-1000 ${showContent && isVisible
          ? "opacity-100 translate-y-0"
          : "opacity-0 translate-y-8"
          }`}
      >
        <div className="w-full max-w-6xl mx-auto px-4 md:px-8">
          {/* Mobile Layout */}
          <div className="md:hidden">
            <div className="max-w-sm mx-auto">
              <AutoScrollCarousel
                isActive={showContent}
                scrollSpeed={35}
                pauseDuration={3000}
                startDelay={1500}
              >
                <div
                  className="flex space-x-4 pb-4"
                  style={{ width: "max-content" }}
                >
                  {aiAgentCategories.map((agent, index) => (
                    <div
                      key={agent.title}
                      className="bg-white/50 dark:bg-gray-800/30 backdrop-blur-sm rounded-lg p-4 transition-all duration-700 theme-transition flex-shrink-0 w-72"
                      style={{ transitionDelay: `${index * 0.15}s` }}
                    >
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="text-dark dark:text-light theme-transition">
                          {agent.icon}
                        </div>
                        <h3 className="text-sm font-semibold text-dark dark:text-slate-100 font-title theme-transition break-words">
                          {agent.title}
                        </h3>
                      </div>
                      <p className="text-xs text-gray-700 dark:text-gray-300 mb-3 font-body theme-transition break-words">
                        {agent.description}
                      </p>
                      <div className="space-y-1">
                        {agent.features.map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="flex items-start space-x-2"
                          >
                            <div className="w-1 h-1 bg-gray-500 dark:bg-gray-400 rounded-full mt-2 flex-shrink-0 theme-transition"></div>
                            <span className="text-xs text-gray-600 dark:text-gray-400 font-body theme-transition break-words">
                              {feature}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </AutoScrollCarousel>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {aiAgentCategories.map((agent, index) => (
                <div
                  key={agent.title}
                  className="bg-white/10 dark:bg-gray-800/30 backdrop-blur-sm rounded-xl p-8 hover:scale-105 transition-all duration-700 hover:shadow-xl theme-transition"
                  style={{ transitionDelay: `${index * 0.15}s` }}
                >
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="text-dark dark:text-light theme-transition">
                      {agent.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-dark dark:text-slate-100 font-title theme-transition break-words">
                      {agent.title}
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-6 font-body theme-transition break-words">
                    {agent.description}
                  </p>
                  <div className="space-y-3">
                    {agent.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-start space-x-3"
                      >
                        <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mt-2 flex-shrink-0 theme-transition"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 font-body theme-transition break-words">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Free AI Audit CTA Button */}
          <div
            className={`text-center mt-12 px-4 transition-all duration-1000 delay-800 ${showContent
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
              }`}
          >
            <FreeAuditButton size="md" className="w-full max-w-xs mx-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}
