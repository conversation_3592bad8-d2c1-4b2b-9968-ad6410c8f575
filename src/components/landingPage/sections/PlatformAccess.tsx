import { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";

export function PlatformAccess() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const features = [
    {
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" />
        </svg>
      ),
      text: "Instant AI Automation",
    },
    {
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" />
        </svg>
      ),
      text: "Real-time Analytics",
    },
    {
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" />
        </svg>
      ),
      text: "Enterprise Security",
    },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
          } else {
            setIsVisible(false);
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: "-10% 0px -10% 0px",
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    const currentRef = sectionRef.current;
    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <div
      ref={sectionRef}
      className="h-screen w-full overflow-hidden relative theme-transition flex items-center justify-center"
    >
      <div className="w-full max-w-4xl mx-auto px-4 md:px-8 text-center">
        {/* Header */}
        <div
          className={`mb-8 sm:mb-10 md:mb-12 transition-all duration-1000 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            }`}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-brand font-bold text-dark dark:text-slate-100 mb-4 sm:mb-6 theme-transition leading-tight">
            Ready to Transform Your Business with AI?
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-gray-600 dark:text-slate-300 font-body max-w-2xl mx-auto leading-relaxed theme-transition px-2">
            Access our AI automation platform and start optimizing your
            workflows today.
          </p>
        </div>

        {/* Features */}
        <div
          className={`mb-8 sm:mb-10 md:mb-12 transition-all duration-1000 delay-200 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            }`}
        >
          <div className="flex flex-col md:flex-row justify-center items-center gap-4 sm:gap-6 md:gap-12">
            {features.map((feature, index) => (
              <div
                key={feature.text}
                className={`flex items-center space-x-3 transition-all duration-700 ${isVisible
                  ? "opacity-100 translate-x-0"
                  : "opacity-0 translate-x-8"
                  }`}
                style={{ transitionDelay: `${0.3 + index * 0.15}s` }}
              >
                <div className="text-dark dark:text-light theme-transition flex-shrink-0">
                  {feature.icon}
                </div>
                <span className="text-sm sm:text-base text-gray-600 dark:text-slate-300 font-body font-medium theme-transition break-words">
                  {feature.text}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Main CTA */}
        <div
          className={`transition-all duration-1000 delay-600 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            }`}
        >
          <div className="space-y-4 sm:space-y-6">
            {/* Primary CTA Button */}
            <button
              onClick={() => navigate("/app")}
              className="bg-dark dark:bg-light text-white dark:text-dark px-8 sm:px-12 md:px-16 py-4 sm:py-5 rounded-full font-body font-semibold text-lg sm:text-xl hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl theme-transition w-full max-w-xs sm:max-w-sm mx-auto"
            >
              Access AI Platform
            </button>
          </div>

          {/* Supporting Text */}
          <p className="text-gray-500 dark:text-slate-400 font-body text-xs sm:text-sm mt-6 sm:mt-8 theme-transition px-4">
            Join thousands of businesses already automating with AI
          </p>

          {/* Contact Information */}
          <div
            className={`mt-8 sm:mt-10 md:mt-12 space-y-4 transition-all duration-1000 delay-800 ${isVisible
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
              }`}
          >
            {/* Contact Methods */}
            <div className="flex sm:flex-row justify-center items-center gap-4 sm:gap-8 md:gap-12">
              {/* Phone */}
              <a
                href="tel:+***********"
                className="flex items-center space-x-3 text-gray-600 dark:text-slate-300 hover:text-dark dark:hover:text-light transition-all duration-300 hover:scale-105 theme-transition group"
                aria-label="Call us at ****** 624 7930"
              >
                <div className="text-dark dark:text-light theme-transition group-hover:scale-110 transition-transform duration-300">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z" />
                  </svg>
                </div>
                <span className="text-sm sm:text-base font-body font-medium">
                  ****** 624 7930
                </span>
              </a>

              {/* Email */}
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-3 text-gray-600 dark:text-slate-300 hover:text-dark dark:hover:text-light transition-all duration-300 hover:scale-105 theme-transition group"
                aria-label="Email <NAME_EMAIL>"
              >
                <div className="text-dark dark:text-light theme-transition group-hover:scale-110 transition-transform duration-300">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" />
                  </svg>
                </div>
                <span className="text-sm sm:text-base font-body font-medium">
                  <EMAIL>
                </span>
              </a>
            </div>

            {/* Social Media */}
            <div className="flex justify-center items-center gap-6 sm:gap-8">
              {/* TikTok */}
              <a
                href="https://tiktok.com/@adeptos.ai"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 dark:text-slate-300 hover:text-dark dark:hover:text-light transition-all duration-300 hover:scale-110 theme-transition"
                aria-label="Follow us on TikTok @adeptos.ai"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M19.59 6.69C18.77 6.24 18.14 5.47 17.86 4.54H16.2V14.06C16.2 15.68 14.92 17 13.3 17C11.68 17 10.4 15.68 10.4 14.06C10.4 12.44 11.68 11.16 13.3 11.16C13.59 11.16 13.87 11.21 14.14 11.3V9.59C13.87 9.55 13.59 9.53 13.3 9.53C10.63 9.53 8.47 11.69 8.47 14.36C8.47 17.03 10.63 19.19 13.3 19.19C15.97 19.19 18.13 17.03 18.13 14.36V8.5C19.14 9.24 20.38 9.65 21.73 9.65V7.92C20.88 7.92 20.1 7.42 19.59 6.69Z" />
                </svg>
              </a>

              {/* Instagram */}
              <a
                href="https://instagram.com/adeptos.ai"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 dark:text-slate-300 hover:text-dark dark:hover:text-light transition-all duration-300 hover:scale-110 theme-transition"
                aria-label="Follow us on Instagram @adeptos.ai"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M7.8 2H16.2C19.4 2 22 4.6 22 7.8V16.2C22 19.4 19.4 22 16.2 22H7.8C4.6 22 2 19.4 2 16.2V7.8C2 4.6 4.6 2 7.8 2ZM7.6 4C5.61 4 4 5.61 4 7.6V16.4C4 18.39 5.61 20 7.6 20H16.4C18.39 20 20 18.39 20 16.4V7.6C20 5.61 18.39 4 16.4 4H7.6ZM17.25 5.5C17.94 5.5 18.5 6.06 18.5 6.75C18.5 7.44 17.94 8 17.25 8C16.56 8 16 7.44 16 6.75C16 6.06 16.56 5.5 17.25 5.5ZM12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z" />
                </svg>
              </a>

              {/* YouTube */}
              <a
                href="https://youtube.com/@adeptosai"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 dark:text-slate-300 hover:text-dark dark:hover:text-light transition-all duration-300 hover:scale-110 theme-transition"
                aria-label="Subscribe to our YouTube channel @adeptosai"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M10 15L15.19 12L10 9V15ZM21.56 7.17C21.69 7.64 21.78 8.27 21.84 9.07C21.91 9.87 21.94 10.56 21.94 11.16L22 12C22 14.19 21.84 15.8 21.56 16.83C21.31 17.73 20.73 18.31 19.83 18.56C19.36 18.69 18.5 18.78 17.18 18.84C15.88 18.91 14.69 18.94 13.59 18.94L12 19C7.81 19 5.2 18.84 4.17 18.56C3.27 18.31 2.69 17.73 2.44 16.83C2.31 16.36 2.22 15.73 2.16 14.93C2.09 14.13 2.06 13.44 2.06 12.84L2 12C2 9.81 2.16 8.2 2.44 7.17C2.69 6.27 3.27 5.69 4.17 5.44C4.64 5.31 5.5 5.22 6.82 5.16C8.12 5.09 9.31 5.06 10.41 5.06L12 5C16.19 5 18.8 5.16 19.83 5.44C20.73 5.69 21.31 6.27 21.56 7.17Z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
