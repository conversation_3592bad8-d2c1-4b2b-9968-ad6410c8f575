import { useEffect, useState, useRef } from "react";
import { FreeAuditButton } from "@/components/shared/FreeAuditButton";
// import { AnimatedSectionTitle } from "@/components/AnimatedSectionTitle";

export function Strategy() {
  const [isVisible, setIsVisible] = useState(false);
  const [showTitle, setShowTitle] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const timeoutRefs = useRef<NodeJS.Timeout[]>([]);

  const methodologySteps = [
    {
      number: "01",
      title: "Assessment",
      description:
        "Deep dive analysis of your current processes and pain points",
      details:
        "We evaluate your existing workflows, identify automation opportunities, and assess technical requirements.",
    },
    {
      number: "02",
      title: "Solution Design",
      description: "Custom AI architecture tailored to your specific needs",
      details:
        "Our experts design scalable AI solutions that integrate seamlessly with your existing systems.",
    },
    {
      number: "03",
      title: "ROI Planning",
      description: "Clear roadmap with measurable outcomes and timeline",
      details:
        "We establish KPIs, success metrics, and provide detailed implementation timelines with expected returns.",
    },
  ];

  // Clear all timeouts
  const clearTimeouts = () => {
    timeoutRefs.current.forEach((timeout) => clearTimeout(timeout));
    timeoutRefs.current = [];
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Clear any existing timeouts
            clearTimeouts();

            // Reset states and start sequence
            setIsVisible(true);
            setShowTitle(true);
            setShowContent(false);

            // After 2 seconds, fade out title
            const titleTimeout = setTimeout(() => {
              setShowTitle(false);
            }, 2000);

            // After 2.5 seconds total, fade in content
            const contentTimeout = setTimeout(() => {
              setShowContent(true);
            }, 2500);

            // Store timeouts for cleanup
            timeoutRefs.current = [titleTimeout, contentTimeout];
          } else {
            // Clear timeouts and reset states when leaving section
            clearTimeouts();
            setIsVisible(false);
            setShowTitle(false);
            setShowContent(false);
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: "-10% 0px -10% 0px",
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    const currentRef = sectionRef.current;
    return () => {
      clearTimeouts();
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <div
      ref={sectionRef}
      className="h-screen w-full overflow-hidden relative theme-transition"
    >
      {/* Animated Section Title */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-1000 ${showTitle && isVisible ? "opacity-100" : "opacity-0"
          }`}
      >
        <div className="text-center">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-brand font-bold text-dark dark:text-slate-100 mb-4 theme-transition px-4 text-center">
            Strategy
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl text-dark dark:text-light font-body theme-transition px-4 text-center">
            Intelligent Planning & Design
          </p>
        </div>
      </div>

      {/* Methodology Content */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-1000 ${showContent && isVisible
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-8"
          }`}
      >
        <div className="w-full max-w-6xl mx-auto px-4 md:px-8">
          {/* Mobile Layout */}
          <div className="md:hidden">
            <div className="space-y-3 max-w-sm mx-auto">
              {methodologySteps.map((step, index) => (
                <div
                  key={step.number}
                  className="bg-white/60 dark:bg-gray-800/30 backdrop-blur-sm rounded-xl p-4 transition-all duration-700 theme-transition"
                  style={{ transitionDelay: `${index * 0.2}s` }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-2xl font-bold text-dark dark:text-light font-brand theme-transition">
                      {step.number}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-dark dark:text-slate-100 mb-2 font-title theme-transition break-words">
                        {step.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-slate-300 font-body leading-relaxed theme-transition break-words">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {methodologySteps.map((step, index) => (
                <div
                  key={step.number}
                  className="bg-white/80 dark:bg-gray-800/30 backdrop-blur-sm rounded-xl p-8 hover:scale-105 transition-all duration-700 hover:shadow-xl theme-transition"
                  style={{ transitionDelay: `${index * 0.2}s` }}
                >
                  <div className="text-4xl font-bold text-dark dark:text-light mb-4 font-brand theme-transition">
                    {step.number}
                  </div>
                  <h3 className="text-xl font-semibold text-dark dark:text-slate-100 mb-4 font-title theme-transition break-words">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 dark:text-slate-300 font-body leading-relaxed mb-4 theme-transition break-words">
                    {step.description}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-slate-400 font-body leading-relaxed theme-transition break-words">
                    {step.details}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Free AI Audit CTA Button */}
          <div
            className={`text-center mt-4 px-4 transition-all duration-1000 delay-800 ${showContent
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-8"
              }`}
          >
            <FreeAuditButton size="sm" className="w-full max-w-xs mx-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}
