import { useEffect, useState, memo } from "react";
import { RotatingContent } from "@/components/shared/RotatingContent";
import { FreeAuditButton } from "@/components/shared/FreeAuditButton";
import logo from "@/assets/adeptos_logo.png";

export const Welcome = memo(function Welcome() {
  const [phase, setPhase] = useState<"loading" | "entrance" | "content">(
    "loading"
  );

  const valueProps = [
    "Automate Complex Business Processes",
    "Transform Data into Intelligent Decisions",
    "Eliminate Repetitive Tasks with AI",
    "Scale Operations with Machine Learning",
    "Optimize Workflows with Smart Automation",
  ];

  useEffect(() => {
    const loadingTimer = setTimeout(() => setPhase("entrance"), 300);
    const entranceTimer = setTimeout(() => setPhase("content"), 1500);

    return () => {
      clearTimeout(loadingTimer);
      clearTimeout(entranceTimer);
    };
  }, []);

  const handleScrollToNext = () => {
    const scrollContainer = document.querySelector(".snap-y");
    if (scrollContainer) {
      const viewportHeight = window.innerHeight;
      scrollContainer.scrollTo({
        top: viewportHeight,
        behavior: "smooth",
      });
    }
  };

  const getLogoClasses = () => {
    const baseClasses = "transition-all duration-2000 ease-out";

    if (phase === "loading") {
      return `${baseClasses} opacity-0 scale-50 rotate-12`;
    }

    if (phase === "entrance") {
      return `${baseClasses} opacity-100 scale-100 rotate-0`;
    }

    return `${baseClasses} opacity-100 scale-75`;
  };

  const getLogoContainerClasses = () => {
    const baseClasses = "absolute transition-all duration-2000 ease-in-out";

    if (phase === "loading" || phase === "entrance") {
      return `${baseClasses} top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2`;
    }

    return `${baseClasses} top-20 left-1/2 transform -translate-x-1/2`;
  };

  return (
    <div className="h-screen w-full overflow-x-hidden relative">
      {/* Logo */}
      <div className={getLogoContainerClasses()}>
        <img
          src={logo}
          alt="Adeptos AI Logo"
          className={`${getLogoClasses()} ${phase === "loading" || phase === "entrance"
            ? "w-48 h-48"
            : "w-48 h-48"
            } drop-shadow-lg hover:scale-105 transition-transform duration-200 relative z-10 logo-animation`}
        />
      </div>

      {/* Minimal Main Content */}
      {phase === "content" && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center w-full max-w-4xl px-4 sm:px-6 z-10">
          <div className="space-y-8 animate-[fadeInUp_1s_ease-out_0.5s_forwards] opacity-0">
            {/* Company Name with Animation */}
            <h1 className="text-4xl sm:text-5xl md:text-6xl text-dark dark:text-white font-brand font-bold tracking-tight theme-transition pt-4">
              <span className="inline-block animate-[slideInLeft_0.8s_ease-out_0.8s_forwards] opacity-0 logo-animation">
                adeptos
              </span>
              <span className="inline-block animate-[slideInRight_0.8s_ease-out_1.2s_forwards] opacity-0 text-gray-600 dark:text-gray-300 logo-animation theme-transition">
                .ai
              </span>
            </h1>

            {/* Rotating Value Propositions */}
            <div className="text-lg sm:text-xl md:text-2xl text-dark dark:text-light font-body font-medium h-8 theme-transition break-words">
              <RotatingContent items={valueProps} interval={3000} />
            </div>

            {/* Free AI Audit CTA Button */}
            <div className="animate-[fadeInUp_1s_ease-out_2.5s_forwards] opacity-0 mt-8 px-4">
              <FreeAuditButton
                size="md"
                className="mx-auto w-full max-w-xs sm:max-w-sm"
              />
            </div>
          </div>
        </div>
      )}

      {/* Scroll Down Arrow */}
      {phase === "content" && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-[fadeInUp_1s_ease-out_2s_forwards] z-10">
          <button
            onClick={handleScrollToNext}
            className="flex flex-col items-center space-y-2 text-dark hover:text-gray dark:text-light dark:hover:text-white transition cursor-pointer"
            aria-label="Scroll down to explore our AI automation services"
          >
            <span className="text-sm font-body">Explore Our AI Solutions</span>
            <div className="w-8 h-8 border-2 border-current rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <svg
                className="w-4 h-4 animate-bounce"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 14l-7 7m0 0l-7-7m7 7V3"
                />
              </svg>
            </div>
          </button>
        </div>
      )}
    </div>
  );
});
