import { useEffect, useState, useCallback } from "react";

interface AnimatedSectionTitleProps {
  sectionIndex: number; // 2, 3, or 4 for Discovery, Strategy, Implementation
  title: string;
  subtitle: string;
  className?: string;
}

export function AnimatedSectionTitle({
  sectionIndex,
  title,
  subtitle,
  className = "",
}: AnimatedSectionTitleProps) {
  const [isActive, setIsActive] = useState(false);
  const [animationPhase, setAnimationPhase] = useState<
    "hidden" | "moving" | "centered"
  >("hidden");

  const handleScroll = useCallback(() => {
    const scrollContainer = document.querySelector(".snap-y");
    if (!scrollContainer) return;

    const scrollTop = scrollContainer.scrollTop;
    const windowHeight = window.innerHeight;

    // Calculate which section we're in
    const currentSectionIndex = Math.floor(scrollTop / windowHeight);

    // Check if this is the active section
    const isCurrentSection = currentSectionIndex === sectionIndex;

    if (isCurrentSection && !isActive) {
      setIsActive(true);
      setAnimationPhase("moving");
      // After a short delay, transition to centered
      setTimeout(() => setAnimationPhase("centered"), 100);
    } else if (!isCurrentSection && isActive) {
      setAnimationPhase("moving");
      setTimeout(() => {
        setIsActive(false);
        setAnimationPhase("hidden");
      }, 300);
    }
  }, [sectionIndex, isActive]);

  useEffect(() => {
    const scrollContainer = document.querySelector(".snap-y");
    if (!scrollContainer) return;

    scrollContainer.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial call

    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  if (!isActive) return null;

  const getAnimationClasses = () => {
    switch (animationPhase) {
      case "moving":
        return "opacity-0 scale-95 translate-y-8";
      case "centered":
        return "opacity-100 scale-100 translate-y-0";
      default:
        return "opacity-0 scale-95 translate-y-8";
    }
  };

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center pointer-events-none z-30 ${className}`}
    >
      <div
        className={`text-center transition-all duration-700 ease-out ${getAnimationClasses()}`}
      >
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-brand font-bold text-dark dark:text-slate-100 mb-4 theme-transition px-4 text-center">
          {title}
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl text-dark dark:text-light font-body theme-transition px-4 text-center">
          {subtitle}
        </p>
      </div>
    </div>
  );
}
