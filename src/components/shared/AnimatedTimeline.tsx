import { useEffect, useState, useCallback, useRef, memo } from "react";

interface TimelineStep {
  id: number;
  title: string;
  subtitle: string;
  icon: React.ReactNode;
}

interface AnimatedTimelineProps {
  steps: TimelineStep[];
  className?: string;
}

export const AnimatedTimeline = memo(function AnimatedTimeline({
  steps,
  className = "",
}: AnimatedTimelineProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Cache DOM elements and values to avoid repeated queries
  const scrollContainerRef = useRef<Element | null>(null);
  const windowHeightRef = useRef(window.innerHeight);
  const throttleTimeoutRef = useRef<number | null>(null);

  // Update window height on resize
  useEffect(() => {
    const handleResize = () => {
      windowHeightRef.current = window.innerHeight;
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Navigation function to scroll to specific section
  const navigateToStep = useCallback((stepIndex: number) => {
    const scrollContainer =
      scrollContainerRef.current || document.querySelector(".snap-y");
    if (scrollContainer) {
      const viewportHeight = windowHeightRef.current;
      // Timeline spans sections 2-4 (indices 1-3), so add 1 to stepIndex
      const targetSectionIndex = stepIndex + 1;
      scrollContainer.scrollTo({
        top: targetSectionIndex * viewportHeight,
        behavior: "smooth",
      });
    }
  }, []);

  // Throttled scroll handler for better performance
  const handleScroll = useCallback(() => {
    // Throttle scroll events to ~16ms (60fps)
    if (throttleTimeoutRef.current) return;

    throttleTimeoutRef.current = requestAnimationFrame(() => {
      throttleTimeoutRef.current = null;

      const scrollContainer =
        scrollContainerRef.current || document.querySelector(".snap-y");
      if (!scrollContainer) return;

      const scrollTop = scrollContainer.scrollTop;
      const windowHeight = windowHeightRef.current;

      // Calculate which section we're in (sections 2-4 are indices 1-3)
      const sectionIndex = Math.floor(scrollTop / windowHeight);

      // Timeline is active for sections 2-4 (indices 1-3)
      if (sectionIndex >= 1 && sectionIndex <= 3) {
        setIsVisible(true);

        // Map section index to timeline step (section 2=step 0, section 3=step 1, section 4=step 2)
        const timelineStepIndex = sectionIndex - 1; // Convert section index to timeline step index

        // Calculate progress within the current section
        const sectionStartScroll = sectionIndex * windowHeight;
        const sectionScrollProgress =
          (scrollTop - sectionStartScroll) / windowHeight;

        // Calculate overall progress across all timeline sections
        // Each section should show 0-100% progress, not divided by total steps
        const overallProgress =
          (timelineStepIndex + sectionScrollProgress) / (steps.length - 1);
        const clampedProgress = Math.max(0, Math.min(1, overallProgress));
        setProgress(clampedProgress);

        // Set current step based on which section we're in
        setCurrentStep(Math.min(steps.length - 1, timelineStepIndex));
      } else {
        setIsVisible(false);
      }
    });
  }, [steps.length]);

  useEffect(() => {
    const scrollContainer = document.querySelector(".snap-y");
    if (!scrollContainer) return;

    // Cache the scroll container reference
    scrollContainerRef.current = scrollContainer;

    scrollContainer.addEventListener("scroll", handleScroll, { passive: true });
    handleScroll();

    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
      // Clean up any pending animation frame
      if (throttleTimeoutRef.current) {
        cancelAnimationFrame(throttleTimeoutRef.current);
        throttleTimeoutRef.current = null;
      }
    };
  }, [handleScroll]);

  if (!isVisible) return null;

  return (
    <>
      {/* Desktop Timeline - Left side vertical layout */}
      <div
        className={`hidden md:block fixed left-4 md:left-8 top-1/2 transform -translate-y-1/2 z-40 ${className}`}
      >
        <div className="relative">
          {/* Main timeline line */}
          <div className="absolute left-6 top-0 w-0.5 h-full bg-gray dark:bg-gray-600 theme-transition">
            {/* Progress line */}
            <div
              className="absolute top-0 left-0 w-full bg-dark dark:bg-light theme-transition timeline-progress"
              style={{
                height: `${progress * 100}%`,
                transformOrigin: "top",
              }}
            />
          </div>

          {/* Timeline steps */}
          <div className="space-y-16">
            {steps.map((step, index) => {
              const isActive = index <= currentStep;
              const isCurrent = index === currentStep;

              return (
                <div
                  key={step.id}
                  className={`relative flex items-center transition-all duration-500 ${
                    isActive ? "opacity-100" : "opacity-100"
                  }`}
                  style={{
                    transitionDelay: `${index * 0.1}s`,
                  }}
                >
                  {/* Step circle */}
                  <button
                    onClick={() => navigateToStep(index)}
                    className={`relative z-10 w-12 h-12 rounded-full border-4 flex items-center justify-center transition-all duration-500 theme-transition cursor-pointer hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark/50 dark:focus:ring-light/50 ${
                      isActive
                        ? "bg-dark dark:bg-light border-dark dark:border-light text-white dark:text-dark hover:bg-gray-800 dark:hover:bg-gray-200"
                        : "bg-white dark:bg-gray-800 border-gray dark:border-gray-600 text-gray dark:text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-dark/50 dark:hover:border-light/50"
                    }`}
                    aria-label={`Navigate to ${step.title} section`}
                    title={`Go to ${step.title}`}
                  >
                    {step.icon}
                  </button>

                  {/* Step content - Only title */}
                  <div
                    className={`ml-4 md:ml-6 transition-all duration-500 timeline-label ${
                      isActive
                        ? "opacity-100 translate-x-0"
                        : "opacity-50 translate-x-4"
                    } ${isCurrent ? "opacity-0" : ""}`}
                    style={{
                      transitionDelay: `${index * 0.1 + 0.2}s`,
                    }}
                  >
                    <h3 className="text-base md:text-lg  font-semibold text-dark dark:text-slate-100 font-title theme-transition">
                      {step.title}
                    </h3>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Mobile Timeline - Bottom horizontal layout */}
      <div className="md:hidden fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
        <div className="relative px-4">
          {/* Calculate the width needed for the timeline */}
          <div
            className="relative"
            style={{ width: `${(steps.length - 1) * 4 + 2.5}rem` }}
          >
            {/* Main timeline line - horizontal */}
            <div className="absolute top-5 left-5 right-5 h-0.5 bg-gray dark:bg-gray-600 theme-transition">
              {/* Progress line */}
              <div
                className="absolute left-0 top-0 h-full bg-dark dark:bg-light theme-transition timeline-progress-mobile"
                style={{
                  width: `${progress * 100}%`,
                  transformOrigin: "left",
                }}
              />
            </div>

            {/* Timeline steps - horizontal layout */}
            <div className="flex justify-between">
              {steps.map((step, index) => {
                const isActive = index <= currentStep;

                return (
                  <div
                    key={step.id}
                    className={`relative flex flex-col items-center transition-all duration-500 ${
                      isActive ? "opacity-100" : "opacity-100"
                    }`}
                    style={{
                      transitionDelay: `${index * 0.1}s`,
                    }}
                  >
                    {/* Step circle - smaller on mobile */}
                    <button
                      onClick={() => navigateToStep(index)}
                      className={`relative z-10 w-10 h-10 timeline-mobile-circle rounded-full border-3 flex items-center justify-center transition-all duration-500 theme-transition cursor-pointer hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-dark/50 dark:focus:ring-light/50 ${
                        isActive
                          ? "bg-dark dark:bg-light border-dark dark:border-light text-white dark:text-dark hover:bg-gray-800 dark:hover:bg-gray-200"
                          : "bg-white dark:bg-gray-800 border-gray dark:border-gray-600 text-gray dark:text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-dark/50 dark:hover:border-light/50"
                      }`}
                      aria-label={`Navigate to ${step.title} section`}
                      title={`Go to ${step.title}`}
                    >
                      <div className="w-5 h-5 flex items-center justify-center">
                        {step.icon}
                      </div>
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </>
  );
});

// Default timeline steps for the process flow
export const defaultTimelineSteps: TimelineStep[] = [
  {
    id: 1,
    title: "Discovery",
    subtitle: "Analyze & understand",
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.74 7.9L7.1 9.26C8.62 7.81 10.81 7 13.38 7C15.1 7 16.77 7.5 18.23 8.39L21 9ZM7.1 14.74C8.62 16.19 10.81 17 13.38 17C15.1 17 16.77 16.5 18.23 15.61L21 15V17L15 23L13.5 21.5L16.17 18.83C15.24 18.94 14.32 19 13.38 19C10.38 19 7.7 17.89 5.74 16.1L7.1 14.74Z" />
      </svg>
    ),
  },
  {
    id: 2,
    title: "Strategy",
    subtitle: "Plan & design",
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C17.52 2 22 6.48 22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12C20 7.58 16.42 4 12 4ZM12 6C15.31 6 18 8.69 18 12C18 15.31 15.31 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6ZM12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12C16 9.79 14.21 8 12 8ZM12 10C13.1 10 14 10.9 14 12C14 13.1 13.1 14 12 14C10.9 14 10 13.1 10 12C10 10.9 10.9 10 12 10Z" />
      </svg>
    ),
  },
  {
    id: 3,
    title: "Implementation",
    subtitle: "Build & deploy",
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 15.5C10.07 15.5 8.5 13.93 8.5 12C8.5 10.07 10.07 8.5 12 8.5C13.93 8.5 15.5 10.07 15.5 12C15.5 13.93 13.93 15.5 12 15.5ZM19.43 12.97C19.47 12.65 19.5 12.33 19.5 12C19.5 11.67 19.47 11.34 19.43 11L21.54 9.37C21.73 9.22 21.78 8.95 21.66 8.73L19.66 5.27C19.54 5.05 19.27 4.96 19.05 5.05L16.56 6.05C16.04 5.65 15.48 5.32 14.87 5.07L14.49 2.42C14.46 2.18 14.25 2 14 2H10C9.75 2 9.54 2.18 9.51 2.42L9.13 5.07C8.52 5.32 7.96 5.66 7.44 6.05L4.95 5.05C4.72 4.96 4.46 5.05 4.34 5.27L2.34 8.73C2.21 8.95 2.27 9.22 2.46 9.37L4.57 11C4.53 11.34 4.5 11.67 4.5 12C4.5 12.33 4.53 12.65 4.57 12.97L2.46 14.63C2.27 14.78 2.21 15.05 2.34 15.27L4.34 18.73C4.46 18.95 4.72 19.03 4.95 18.95L7.44 17.94C7.96 18.34 8.52 18.68 9.13 18.93L9.51 21.58C9.54 21.82 9.75 22 10 22H14C14.25 22 14.46 21.82 14.49 21.58L14.87 18.93C15.48 18.68 16.04 18.34 16.56 17.94L19.05 18.95C19.27 19.03 19.54 18.95 19.66 18.73L21.66 15.27C21.78 15.05 21.73 14.78 21.54 14.63L19.43 12.97Z" />
      </svg>
    ),
  },
];
