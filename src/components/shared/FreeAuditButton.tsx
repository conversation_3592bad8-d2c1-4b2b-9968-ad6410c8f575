interface FreeAuditButtonProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "secondary";
  onClick?: () => void;
}

export function FreeAuditButton({
  className = "",
  size = "sm",
  variant = "primary",
  onClick,
}: FreeAuditButtonProps) {
  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "px-6 py-3 text-sm min-h-[44px]";
      case "lg":
        return "px-8 sm:px-12 md:px-16 py-4 sm:py-5 text-lg sm:text-xl min-h-[48px]";
      default:
        return "px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg min-h-[44px]";
    }
  };

  const getVariantClasses = () => {
    if (variant === "secondary") {
      return "bg-white/10 dark:bg-gray-800/30 text-dark dark:text-light border-2 border-dark/20 dark:border-light/20 hover:bg-white/20 dark:hover:bg-gray-800/50 hover:border-dark/40 dark:hover:border-light/40";
    }

    return "bg-dark dark:bg-light text-white dark:text-dark hover:bg-gray-800 dark:hover:bg-gray-200";
  };

  const handleClick = () => {
    // Scroll to the PlatformAccess section (last section)
    const platformSection = document.querySelector(
      ".snap-y .snap-start:last-child"
    );
    if (platformSection) {
      platformSection.scrollIntoView({ behavior: "smooth" });
    }

    // Call custom onClick if provided
    onClick?.();
  };

  return (
    <button
      onClick={handleClick}
      className={`
        ${getSizeClasses()}
        ${getVariantClasses()}
        rounded-full 
        font-body 
        font-semibold 
        transition-all 
        duration-300 
        hover:scale-105 
        shadow-lg 
        hover:shadow-xl 
        theme-transition 
        backdrop-blur-sm
        focus:outline-none 
        focus:ring-2 
        focus:ring-dark/50 
        dark:focus:ring-light/50 
        focus:ring-offset-2 
        focus:ring-offset-transparent
        ${className}
      `}
      aria-label="Get your Free AI Business Audit - scroll to platform access section"
    >
      Click Here for a Free AI Business Audit
    </button>
  );
}
