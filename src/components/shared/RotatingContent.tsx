import { useEffect, useState, memo } from "react";

interface RotatingContentProps {
  items: string[];
  interval: number;
}

export const RotatingContent = memo(function RotatingContent({
  items,
  interval,
}: RotatingContentProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setInterval(() => {
      setIsVisible(false);
      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % items.length);
        setIsVisible(true);
      }, 300);
    }, interval);

    return () => clearInterval(timer);
  }, [items.length, interval]);

  return (
    <div
      className={`transition-opacity duration-300 break-words ${
        isVisible ? "opacity-100" : "opacity-0"
      }`}
    >
      {items[currentIndex]}
    </div>
  );
});
