import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

interface Phase1Props {
  onNext: () => void;
  formData?: BusinessProfileData;
  setFormData: (data: BusinessProfileData) => void;
}

const businessProfileSchema = z.object({
  industry: z.string().min(1, "Please select your industry"),
  employeeCount: z.string().min(1, "Please select your employee count"),
  businessType: z.string().min(1, "Please select what you sell"),
  monthlyRevenue: z.string().min(1, "Please select your monthly revenue range"),
  salesLocation: z.string().min(1, "Please select where you sell"),
});

export type BusinessProfileData = z.infer<typeof businessProfileSchema>;

const industryOptions = [
  { value: "", label: "Select your industry" },
  { value: "technology", label: "Technology" },
  { value: "healthcare", label: "Healthcare" },
  { value: "finance", label: "Finance" },
  { value: "retail", label: "Retail" },
  { value: "manufacturing", label: "Manufacturing" },
  { value: "education", label: "Education" },
  { value: "real-estate", label: "Real Estate" },
  { value: "consulting", label: "Consulting" },
  { value: "marketing", label: "Marketing" },
  { value: "legal", label: "Legal" },
  { value: "hospitality", label: "Hospitality" },
  { value: "construction", label: "Construction" },
  { value: "automotive", label: "Automotive" },
  { value: "food-beverage", label: "Food & Beverage" },
  { value: "other", label: "Other" },
];

const employeeCountOptions = [
  { value: "", label: "Select employee count" },
  { value: "1-10", label: "1-10 employees" },
  { value: "11-50", label: "11-50 employees" },
  { value: "51-200", label: "51-200 employees" },
  { value: "201-500", label: "201-500 employees" },
  { value: "500+", label: "500+ employees" },
];

const businessTypeOptions = [
  { value: "", label: "Select what you sell" },
  { value: "services", label: "Services" },
  { value: "products", label: "Products" },
  { value: "both", label: "Both" },
];

const monthlyRevenueOptions = [
  { value: "", label: "Select monthly revenue range" },
  { value: "<10k", label: "Less than $10K" },
  { value: "10k-50k", label: "$10K - $50K" },
  { value: "50k-100k", label: "$50K - $100K" },
  { value: "100k-500k", label: "$100K - $500K" },
  { value: "500k+", label: "$500K+" },
];

const salesLocationOptions = [
  { value: "", label: "Select where you sell" },
  { value: "physical", label: "Physical location" },
  { value: "online", label: "Online" },
  { value: "both", label: "Both" },
];

export function Phase1({ onNext, formData, setFormData }: Phase1Props) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<BusinessProfileData>({
    resolver: zodResolver(businessProfileSchema),
    defaultValues: {
      industry: formData?.industry || "",
      employeeCount: formData?.employeeCount || "",
      businessType: formData?.businessType || "",
      monthlyRevenue: formData?.monthlyRevenue || "",
      salesLocation: formData?.salesLocation || "",
    },
  });

  const onSubmit = async (data: BusinessProfileData) => {
    setFormData(data);
    onNext();
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 min-h-[calc(100dvh-8rem)] md:min-h-auto">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            See How AI Can Unlock Growth in Your Business – Instantly.
          </h1>
        </div>

        {/* Phase Title and Subtitle */}
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-title font-bold text-dark dark:text-white theme-transition">
            Phase 1: Business Profile
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition leading-relaxed">
            Complete these questions so our AI Agent can create a personalized
            report for your business.
          </p>
        </div>

        {/* Form Section */}
        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Industry */}
            <div className="space-y-2">
              <label
                htmlFor="industry"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                What Industry are you in? *
              </label>
              <select
                id="industry"
                {...register("industry")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  errors.industry
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
              >
                {industryOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    className="bg-white dark:bg-gray text-dark dark:text-white font-body"
                  >
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.industry && (
                <p className="text-red-500 text-sm font-body">
                  {errors.industry.message}
                </p>
              )}
            </div>

            {/* Employee Count */}
            <div className="space-y-2">
              <label
                htmlFor="employeeCount"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                How many employees do you have? *
              </label>
              <select
                id="employeeCount"
                {...register("employeeCount")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  errors.employeeCount
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
              >
                {employeeCountOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    className="bg-white dark:bg-gray text-dark dark:text-white font-body"
                  >
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.employeeCount && (
                <p className="text-red-500 text-sm font-body">
                  {errors.employeeCount.message}
                </p>
              )}
            </div>

            {/* Business Type */}
            <div className="space-y-2">
              <label
                htmlFor="businessType"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Do you sell services, products or both? *
              </label>
              <select
                id="businessType"
                {...register("businessType")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  errors.businessType
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
              >
                {businessTypeOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    className="bg-white dark:bg-gray text-dark dark:text-white font-body"
                  >
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.businessType && (
                <p className="text-red-500 text-sm font-body">
                  {errors.businessType.message}
                </p>
              )}
            </div>

            {/* Monthly Revenue */}
            <div className="space-y-2">
              <label
                htmlFor="monthlyRevenue"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                What is your average monthly revenue? *
              </label>
              <select
                id="monthlyRevenue"
                {...register("monthlyRevenue")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  errors.monthlyRevenue
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
              >
                {monthlyRevenueOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    className="bg-white dark:bg-gray text-dark dark:text-white font-body"
                  >
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.monthlyRevenue && (
                <p className="text-red-500 text-sm font-body">
                  {errors.monthlyRevenue.message}
                </p>
              )}
            </div>

            {/* Sales Location */}
            <div className="space-y-2">
              <label
                htmlFor="salesLocation"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                Do you sell to customers in a physical location, online or both?
                *
              </label>
              <select
                id="salesLocation"
                {...register("salesLocation")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 ${
                  errors.salesLocation
                    ? "border-red-500 focus:border-red-500"
                    : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                }`}
              >
                {salesLocationOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                    className="bg-white dark:bg-gray text-dark dark:text-white font-body"
                  >
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.salesLocation && (
                <p className="text-red-500 text-sm font-body">
                  {errors.salesLocation.message}
                </p>
              )}
            </div>

            {/* Primary CTA Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`
                  px-8 sm:px-12 md:px-16 py-4 sm:py-5
                  text-lg sm:text-xl font-body font-bold
                  bg-dark dark:bg-light
                  text-white dark:text-dark
                  rounded-full
                  transition-all duration-300
                  hover:scale-105
                  shadow-lg hover:shadow-xl
                  theme-transition
                  backdrop-blur-sm
                  focus:outline-none
                  focus:ring-2
                  focus:ring-dark/50 dark:focus:ring-light/50
                  focus:ring-offset-2
                  focus:ring-offset-transparent
                  disabled:opacity-50
                  disabled:cursor-not-allowed
                  disabled:hover:scale-100
                  min-h-[56px] sm:min-h-[64px]
                  w-full max-w-md mx-auto
                  ${
                    isSubmitting
                      ? "hover:bg-dark dark:hover:bg-light"
                      : "hover:bg-gray-800 dark:hover:bg-gray-200"
                  }
                `}
                aria-label="Continue to next phase"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  "Next"
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Privacy/Terms Notice */}
        <div className="text-center max-w-3xl mx-auto">
          <p className="text-xs sm:text-sm font-body text-gray dark:text-gray theme-transition leading-relaxed">
            Your information is secure and will only be used to generate your
            personalized AI business report.
          </p>
        </div>
      </div>
    </div>
  );
}
