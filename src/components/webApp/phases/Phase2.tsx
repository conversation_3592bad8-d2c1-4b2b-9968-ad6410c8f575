import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

interface Phase2Props {
  onNext: () => void;
  formData?: ChallengesData;
  setFormData: (data: ChallengesData) => void;
}

const challengesSchema = z.object({
  timeConsumingTasks: z
    .string()
    .min(1, "Please describe your most time consuming tasks")
    .min(10, "Please provide more detail (at least 10 characters)")
    .max(1000, "Please keep your response under 1000 characters"),
  painPoints: z
    .string()
    .min(1, "Please describe your biggest pain points")
    .min(10, "Please provide more detail (at least 10 characters)")
    .max(1000, "Please keep your response under 1000 characters"),
  bottlenecks: z
    .string()
    .min(1, "Please describe your biggest bottlenecks")
    .min(20, "Please provide more detail (at least 20 characters)")
    .max(1500, "Please keep your response under 1500 characters"),
});

export type ChallengesData = z.infer<typeof challengesSchema>;

export function Phase2({ onNext, formData, setFormData }: Phase2Props) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ChallengesData>({
    resolver: zodResolver(challengesSchema),
    defaultValues: {
      timeConsumingTasks: formData?.timeConsumingTasks || "",
      painPoints: formData?.painPoints || "",
      bottlenecks: formData?.bottlenecks || "",
    },
  });

  const onSubmit = async (data: ChallengesData) => {
    setFormData(data);
    onNext();
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 min-h-[calc(100dvh-8rem)] md:min-h-auto">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            See How AI Can Unlock Growth in Your Business – Instantly.
          </h1>
        </div>

        {/* Phase Title and Subtitle */}
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-title font-bold text-dark dark:text-white theme-transition">
            Phase 2: Challenges and Bottlenecks
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition leading-relaxed">
            This section helps us identify key challenges your business is facing. Tell us about the biggest pain points in your business right now.
          </p>
        </div>

        {/* Form Section */}
        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Time Consuming Tasks */}
            <div className="space-y-2">
              <label
                htmlFor="timeConsumingTasks"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                List 3 of the most time consuming tasks your team carries out on a day to day basis. *
              </label>
              <textarea
                id="timeConsumingTasks"
                rows={4}
                {...register("timeConsumingTasks")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 resize-vertical ${errors.timeConsumingTasks
                  ? "border-red-500 focus:border-red-500"
                  : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                  }`}
                placeholder="Example: 1. Manual data entry from emails into our CRM system (2-3 hours daily)&#10;2. Creating weekly reports by copying data from multiple spreadsheets (4-5 hours weekly)&#10;3. Following up with leads via phone calls and emails (6-8 hours daily)"
              />
              {errors.timeConsumingTasks && (
                <p className="text-red-500 text-sm font-body">
                  {errors.timeConsumingTasks.message}
                </p>
              )}
            </div>

            {/* Pain Points */}
            <div className="space-y-2">
              <label
                htmlFor="painPoints"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                What are the 3 biggest pain points your company is currently facing? *
              </label>
              <textarea
                id="painPoints"
                rows={4}
                {...register("painPoints")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 resize-vertical ${errors.painPoints
                  ? "border-red-500 focus:border-red-500"
                  : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                  }`}
                placeholder="Example: 1. We're losing potential customers because our response time is too slow&#10;2. Our team spends too much time on administrative tasks instead of revenue-generating activities&#10;3. We struggle to track and follow up with leads effectively, causing missed opportunities"
              />
              {errors.painPoints && (
                <p className="text-red-500 text-sm font-body">
                  {errors.painPoints.message}
                </p>
              )}
            </div>

            {/* Bottlenecks */}
            <div className="space-y-2">
              <label
                htmlFor="bottlenecks"
                className="block text-sm font-semibold text-dark dark:text-light theme-transition"
              >
                What are your biggest Bottlenecks right now? (Write a short paragraph about what's holding you back, be as specific as possible). *
              </label>
              <textarea
                id="bottlenecks"
                rows={5}
                {...register("bottlenecks")}
                className={`w-full px-4 py-3 rounded-lg border-2 bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 transition-all duration-200 resize-vertical ${errors.bottlenecks
                  ? "border-red-500 focus:border-red-500"
                  : "border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light"
                  }`}
                placeholder="Example: Our biggest bottleneck is our manual lead qualification process. When a potential customer fills out our contact form, it sits in our inbox for hours before someone manually reviews it, determines if it's a qualified lead, and assigns it to the right salesperson. This delay means we often lose hot prospects to competitors who respond faster. Additionally, our sales team wastes time calling unqualified leads because our initial screening process isn't thorough enough. This creates a cycle where we're both too slow with good leads and too inefficient with our sales team's time."
              />
              {errors.bottlenecks && (
                <p className="text-red-500 text-sm font-body">
                  {errors.bottlenecks.message}
                </p>
              )}
            </div>

            {/* Primary CTA Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`
                  px-8 sm:px-12 md:px-16 py-4 sm:py-5
                  text-lg sm:text-xl font-body font-bold
                  bg-dark dark:bg-light
                  text-white dark:text-dark
                  rounded-full
                  transition-all duration-300
                  hover:scale-105
                  shadow-lg hover:shadow-xl
                  theme-transition
                  backdrop-blur-sm
                  focus:outline-none
                  focus:ring-2
                  focus:ring-dark/50 dark:focus:ring-light/50
                  focus:ring-offset-2
                  focus:ring-offset-transparent
                  disabled:opacity-50
                  disabled:cursor-not-allowed
                  disabled:hover:scale-100
                  min-h-[56px] sm:min-h-[64px]
                  w-full max-w-md mx-auto
                  ${isSubmitting
                    ? "hover:bg-dark dark:hover:bg-light"
                    : "hover:bg-gray-800 dark:hover:bg-gray-200"
                  }
                `}
                aria-label="Continue to verification"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Creating Audit...</span>
                  </div>
                ) : (
                  "Continue"
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Privacy/Terms Notice */}
        <div className="text-center max-w-3xl mx-auto">
          <p className="text-xs sm:text-sm font-body text-gray dark:text-gray theme-transition leading-relaxed">
            Your responses will be analyzed by our AI to create a personalized business automation report with specific recommendations for your company.
          </p>
        </div>
      </div>
    </div>
  );
}
