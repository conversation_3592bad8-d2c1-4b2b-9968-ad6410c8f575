import { Welcome } from "@/components/landingPage/sections/Welcome";
import { Discovery } from "@/components/landingPage/sections/Discovery";
import { Strategy } from "@/components/landingPage/sections/Strategy";
import { Implementation } from "@/components/landingPage/sections/Implementation";
import { Achievements } from "@/components/landingPage/sections/Achievements";
import { PlatformAccess } from "@/components/landingPage/sections/PlatformAccess";
import {
  AnimatedTimeline,
  defaultTimelineSteps,
} from "@/components/shared/AnimatedTimeline";

export function LandingPage() {
  return (
    <main className="landing-page-container h-screen overflow-y-scroll snap-y snap-mandatory relative z-10">
      {/* Animated Timeline - spans sections 2-4 */}
      <AnimatedTimeline steps={defaultTimelineSteps} />

      {/* Section 1: Welcome */}
      <section className="snap-start h-screen min-h-screen">
        <Welcome />
      </section>

      {/* Section 2: Discovery */}
      <section className="snap-start h-screen min-h-screen">
        <Discovery />
      </section>

      {/* Section 3: Strategy */}
      <section className="snap-start h-screen min-h-screen">
        <Strategy />
      </section>

      {/* Section 4: Implementation */}
      <section className="snap-start h-screen min-h-screen">
        <Implementation />
      </section>

      {/* Section 5: Achievements */}
      <section className="snap-start h-screen min-h-screen">
        <Achievements />
      </section>

      {/* Section 6: Platform Access CTA */}
      <section className="snap-start h-screen min-h-screen">
        <PlatformAccess />
      </section>
    </main>
  );
}
